'use client';

import { columns } from '@/app/(protected)/admin/inventory/components/columns';
import TablePagination, { TableContent } from '@/components/table';
import { Button } from '@/components/ui/button';
import { useInventory } from '@/modules/inventory';
import { getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table';
import Link from 'next/link';
import { parseAsInteger, useQueryState } from 'nuqs';

export function InventoryDataTable() {
    const [currentPage] = useQueryState('page', parseAsInteger.withDefault(1));
    const offset = (currentPage - 1) * 10;
    const limit = 10;
    const inventories = useInventory({
        offset,
        limit,
    });
    const totalRowCount = inventories.data?.data.pagination.total || 0;

    const table = useReactTable({
        data: inventories.data?.data.inventory || [],
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true,
        rowCount: totalRowCount,
        state: {
            pagination: {
                pageIndex: currentPage - 1,
                pageSize: limit,
            },
        },
    });

    return (
        <div>
            <div className="mb-4 flex items-center justify-between">
                <h1 className="text-3xl font-medium">Inventory</h1>
                <Button asChild>
                    <Link href="/admin/inventory/add">Add Inventory</Link>
                </Button>
            </div>
            <TableContent table={table} query={inventories} columns={columns} />
            <TablePagination table={table} currentPage={1} totalItems={inventories.data?.data.pagination.total || 1} />
        </div>
    );
}
