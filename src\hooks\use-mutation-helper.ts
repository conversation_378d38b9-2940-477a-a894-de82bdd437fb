import { UseMutationResult } from '@tanstack/react-query';
import { useEffect } from 'react';
import { toast } from 'sonner';

type MutationHelperProps = {
    mutation: UseMutationResult<any, Error, any>;
    successMessage?: string;
    errorMessage?: string;
    onSuccess?: (data: any) => void;
    onError?: (error: any) => void;
    showSuccessToast?: boolean;
    showErrorToast?: boolean;
};

const useMutationHelper = ({
    mutation,
    onSuccess,
    onError,
    successMessage,
    errorMessage,
    showSuccessToast = false,
    showErrorToast = false,
}: MutationHelperProps) => {
    useEffect(() => {
        if (mutation.status == 'success') {
            if (showSuccessToast) toast.success(successMessage ?? mutation.data.message);
            if (onSuccess) onSuccess(mutation.data);
        }
        if (mutation.status == 'error') {
            if (showErrorToast) toast.error(errorMessage ?? mutation.error?.message);
            if (onError) onError(mutation.error);
        }
    }, [mutation.status]);
};

export default useMutationHelper;
