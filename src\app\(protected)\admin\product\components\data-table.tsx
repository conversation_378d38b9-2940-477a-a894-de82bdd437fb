'use client';

import { columns } from '@/app/(protected)/admin/product/components/columns';
import TablePagination, { TableContent } from '@/components/table';
import { Button } from '@/components/ui/button';
import { useProducts } from '@/modules/product';
import { getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table';
import Link from 'next/link';
import { parseAsInteger, useQueryState } from 'nuqs';

export function DataTable() {
    const [currentPage] = useQueryState('page', parseAsInteger.withDefault(1));
    const offset = (currentPage - 1) * 10;
    const limit = 10;
    const products = useProducts({
        offset,
        limit,
    });
    const totalRowCount = products.data?.data.pagination.total || 0;

    const table = useReactTable({
        data: products.data?.data.products || [],
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true,
        rowCount: totalRowCount,
        state: {
            pagination: {
                pageIndex: currentPage - 1,
                pageSize: limit,
            },
        },
    });

    return (
        <div>
            <div className="mb-4 flex items-center justify-between">
                <h1 className="text-3xl font-medium">Products</h1>
                <Button asChild>
                    <Link href="/admin/product/add">Add Product</Link>
                </Button>
            </div>
            <TableContent table={table} query={products} columns={columns} />
            <TablePagination table={table} currentPage={1} totalItems={products.data?.data.pagination.total || 1} />
        </div>
    );
}
