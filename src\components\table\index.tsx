import { Button } from '@/components/ui/button';
import { Pa<PERSON>ation, PaginationContent, PaginationEllipsis, PaginationItem } from '@/components/ui/pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { usePagination } from '@/hooks/use-pagination';
import { cn } from '@/lib/utils';
import { UseQueryResult } from '@tanstack/react-query';
import { CellContext, ColumnDef, flexRender, Table as TableType } from '@tanstack/react-table';
import { CheckCircleIcon, XCircleIcon } from 'lucide-react';
import { parseAsInteger, useQueryState } from 'nuqs';

type TableContentProps<TData, TValue> = {
    table: TableType<any>;
    query: UseQueryResult<any>;
    columns: ColumnDef<TData, TValue>[];
};

export function TableContent<TData, TValue>({ table, query, columns }: TableContentProps<TData, TValue>) {
    return (
        <div className="rounded-md border">
            {query.isLoading ? (
                <TableLoader />
            ) : query.isError ? (
                <TableError query={query} message="Error fetching items. Please try again." />
            ) : (
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-24 text-center">
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            )}
        </div>
    );
}

export function TableError({ query, message }: { query: UseQueryResult<any>; message: string }) {
    return (
        <div className="border-destructive flex h-132.5 flex-col items-center justify-center gap-4 rounded-md border border-dashed pt-4">
            <p className="text-center text-lg font-semibold">{message}</p>
            <div className="flex justify-center">
                <Button onClick={() => query.refetch()}>Retry</Button>
            </div>
        </div>
    );
}

export function TableLoader() {
    return <Skeleton className="h-132.5 w-full rounded-md border" />;
}

type PaginationProps<TData> = {
    table: TableType<TData>;
    currentPage: number;
    totalItems: number;
    paginationItemsToDisplay?: number;
};

export default function TablePagination<T>({
    table,
    currentPage,
    totalItems,
    paginationItemsToDisplay = 5,
}: PaginationProps<T>) {
    const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1));
    const totalPages = Math.ceil(totalItems / 10);
    const { pages, showLeftEllipsis, showRightEllipsis } = usePagination({
        currentPage,
        totalPages,
        paginationItemsToDisplay,
    });

    return (
        <div className="flex items-center justify-between gap-3 py-4">
            <div>
                {page} of {totalPages} pages
            </div>
            <Pagination>
                <PaginationContent>
                    <PaginationItem>
                        <Button
                            variant="secondary"
                            onClick={() => {
                                table.previousPage();
                                setPage(page - 1);
                            }}
                            disabled={!table.getCanPreviousPage()}
                        >
                            Previous
                        </Button>
                    </PaginationItem>
                    {showLeftEllipsis && (
                        <PaginationItem>
                            <PaginationEllipsis />
                        </PaginationItem>
                    )}
                    {pages.map((page) => (
                        <PaginationItem key={page}>
                            <Button
                                variant={page === currentPage ? 'default' : 'secondary'}
                                onClick={() => {
                                    table.previousPage();
                                    setPage(page);
                                }}
                                disabled={page === currentPage}
                            >
                                {page}
                            </Button>
                        </PaginationItem>
                    ))}
                    {showRightEllipsis && (
                        <PaginationItem>
                            <PaginationEllipsis />
                        </PaginationItem>
                    )}
                    <PaginationItem>
                        <Button
                            variant="secondary"
                            onClick={() => {
                                setPage(page + 1);
                                table.nextPage();
                            }}
                            disabled={!table.getCanNextPage()}
                        >
                            Next
                        </Button>
                    </PaginationItem>
                </PaginationContent>
            </Pagination>
        </div>
    );
}

export function TableCellStatus({ row }: CellContext<any, unknown>) {
    const Icon = row.original.status == 'ACTIVE' ? CheckCircleIcon : XCircleIcon;
    return (
        <div
            className={cn('flex items-center', {
                'text-emerald-600': row.original.status == 'ACTIVE',
                'text-red-600': row.original.status == 'INACTIVE',
                'text-yellow-600': row.original.status == 'PENDING',
            })}
        >
            <Icon className="mr-1 h-4 w-4" />
            {row.original.status}
        </div>
    );
}
