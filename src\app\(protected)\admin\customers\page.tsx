'use client';

import { CustomerEditDialog } from '@/components/customer-edit-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Download, Edit, FileSpreadsheet, FileText, MoreHorizontal, Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Customer {
    id: string;
    name: string;
    mobile: string;
    bankName: string;
    branchName: string;
    branchCode: string;
}

// Mock customer data
const initialCustomers: Customer[] = [
    {
        id: 'CUST-001',
        name: '<PERSON>',
        mobile: '+****************',
        bankName: 'Chase Bank',
        branchName: 'Downtown Branch',
        branchCode: 'CH001',
    },
    {
        id: 'CUST-002',
        name: 'Sarah Johnson',
        mobile: '+****************',
        bankName: 'Bank of America',
        branchName: 'Main Street Branch',
        branchCode: 'BOA002',
    },
    {
        id: 'CUST-003',
        name: 'Mike Wilson',
        mobile: '+****************',
        bankName: 'Wells Fargo',
        branchName: 'Central Branch',
        branchCode: 'WF003',
    },
    {
        id: 'CUST-004',
        name: 'Emily Davis',
        mobile: '+****************',
        bankName: 'Citibank',
        branchName: 'North Branch',
        branchCode: 'CITI004',
    },
    {
        id: 'CUST-005',
        name: 'David Brown',
        mobile: '+****************',
        bankName: 'TD Bank',
        branchName: 'South Branch',
        branchCode: 'TD005',
    },
];

export default function CustomersPage() {
    const [customers, setCustomers] = useState<Customer[]>(initialCustomers);
    const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>(initialCustomers);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

    // Filter customers based on search term
    const handleSearch = (term: string) => {
        setSearchTerm(term);
        if (term === '') {
            setFilteredCustomers(customers);
        } else {
            const filtered = customers.filter(
                (customer) =>
                    customer.name.toLowerCase().includes(term.toLowerCase()) ||
                    customer.mobile.includes(term) ||
                    customer.bankName.toLowerCase().includes(term.toLowerCase()) ||
                    customer.branchName.toLowerCase().includes(term.toLowerCase()) ||
                    customer.branchCode.toLowerCase().includes(term.toLowerCase()),
            );
            setFilteredCustomers(filtered);
        }
    };

    // Handle customer edit
    const handleEditCustomer = (customer: Customer) => {
        setSelectedCustomer(customer);
        setIsEditDialogOpen(true);
    };

    // Handle customer save
    const handleSaveCustomer = (updatedCustomer: Customer) => {
        const updatedCustomers = customers.map((customer) =>
            customer.id === updatedCustomer.id ? updatedCustomer : customer,
        );
        setCustomers(updatedCustomers);
        setFilteredCustomers(updatedCustomers);
    };

    // Handle customer delete
    const handleDeleteCustomer = (customerId: string) => {
        const updatedCustomers = customers.filter((customer) => customer.id !== customerId);
        setCustomers(updatedCustomers);
        setFilteredCustomers(updatedCustomers);
    };

    // Export functions
    const exportToPDF = () => {
        // In a real app, this would generate and download a PDF
        console.log('Exporting to PDF...');
        alert('PDF export functionality would be implemented here');
    };

    const exportToCSV = () => {
        // In a real app, this would generate and download a CSV
        const csvContent = [
            ['Name', 'Mobile No.', 'Bank Name', 'Branch Name', 'Branch Code'],
            ...filteredCustomers.map((customer) => [
                customer.name,
                customer.mobile,
                customer.bankName,
                customer.branchName,
                customer.branchCode,
            ]),
        ]
            .map((row) => row.join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'customers.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-foreground text-3xl font-bold">Customers</h1>
                    <p className="text-muted-foreground mt-2">Manage your customer database and export reports.</p>
                </div>
                <Button onClick={() => setIsEditDialogOpen(true)} className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Customer
                </Button>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Customer Database</CardTitle>
                    <div className="flex items-center justify-between gap-4">
                        <div className="relative max-w-sm flex-1">
                            <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
                            <Input
                                placeholder="Search customers..."
                                value={searchTerm}
                                onChange={(e) => handleSearch(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                        <div className="flex gap-2">
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" className="gap-2 bg-transparent">
                                        <Download className="h-4 w-4" />
                                        Export
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                    <DropdownMenuItem onClick={exportToPDF} className="gap-2">
                                        <FileText className="h-4 w-4" />
                                        Export as PDF
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={exportToCSV} className="gap-2">
                                        <FileSpreadsheet className="h-4 w-4" />
                                        Export as CSV
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Mobile No.</TableHead>
                                    <TableHead>Bank Name</TableHead>
                                    <TableHead>Branch Name</TableHead>
                                    <TableHead>Branch Code</TableHead>
                                    <TableHead className="text-right">Action</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredCustomers.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={6} className="text-muted-foreground py-8 text-center">
                                            No customers found
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    filteredCustomers.map((customer) => (
                                        <TableRow key={customer.id}>
                                            <TableCell className="font-medium">{customer.name}</TableCell>
                                            <TableCell>{customer.mobile}</TableCell>
                                            <TableCell>{customer.bankName}</TableCell>
                                            <TableCell>{customer.branchName}</TableCell>
                                            <TableCell>
                                                <Badge variant="outline">{customer.branchCode}</Badge>
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem
                                                            onClick={() => handleEditCustomer(customer)}
                                                            className="gap-2"
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                            Edit
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            onClick={() => handleDeleteCustomer(customer.id)}
                                                            className="text-destructive gap-2"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>

                    {filteredCustomers.length > 0 && (
                        <div className="mt-4 flex items-center justify-between">
                            <p className="text-muted-foreground text-sm">
                                Showing {filteredCustomers.length} of {customers.length} customers
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>

            <CustomerEditDialog
                customer={selectedCustomer}
                open={isEditDialogOpen}
                onOpenChange={(open) => {
                    setIsEditDialogOpen(open);
                    if (!open) setSelectedCustomer(null);
                }}
                onSave={handleSaveCustomer}
            />
        </div>
    );
}
