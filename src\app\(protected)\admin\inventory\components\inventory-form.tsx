'use client';
import { ProductCombobox } from '@/app/(protected)/admin/product/components/product-combobox';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useCreateInventory, useGetInventory, useUpdateInventory } from '@/modules/inventory';
import { CreateInventorySchema, createInventorySchema } from '@/modules/inventory/schema';
import { useProducts } from '@/modules/product';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

type PageParams = {
    inventoryId: string;
};

export default function InventoryAddEditForm() {
    const params = useParams<PageParams>();
    const isCreate = params.inventoryId === 'create' || params.inventoryId === 'add';
    const router = useRouter();
    void useProducts({ limit: 1000 });
    const create = useCreateInventory();
    const update = useUpdateInventory();
    const fn = isCreate ? create : update;
    const inventoryData = useGetInventory(params.inventoryId);

    const form = useForm({
        resolver: zodResolver(createInventorySchema),
        values:
            !isCreate && inventoryData.data
                ? {
                      price: inventoryData.data?.data.inventory.price,
                      quantity: inventoryData.data?.data.inventory.quantity,
                      productId: inventoryData.data?.data.inventory.productId,
                  }
                : {
                      price: 0,
                      quantity: 0,
                      productId: '',
                  },
    });

    const submit = (data: CreateInventorySchema) => {
        const payload: any = {
            ...data,
        };
        if (!isCreate) payload.inventoryId = params.inventoryId;

        fn.mutate(payload, {
            onSuccess: () => {
                toast.success(`Inventory ${isCreate ? 'created' : 'updated'} successfully`);
                if (isCreate) router.push('/admin/inventory');
            },
        });
    };

    if (inventoryData.isLoading)
        return (
            <div className="flex items-center justify-center">
                <Loader2 className="size-12 animate-spin" />
            </div>
        );
    return (
        <div className="">
            <Form {...form}>
                <form onSubmit={form.handleSubmit(submit)} className="mx-auto max-w-3xl space-y-4">
                    <FormField
                        control={form.control}
                        name="productId"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Product</FormLabel>
                                <FormControl>
                                    <ProductCombobox value={field.value || ''} onValueChange={field.onChange} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <FormField
                            control={form.control}
                            name="price"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Price</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="number"
                                            placeholder="Price"
                                            {...field}
                                            value={String(field.value)}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="quantity"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Quantity</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="number"
                                            placeholder="Quantity"
                                            {...field}
                                            value={String(field.value)}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <Button disabled={fn.isPending} type="submit" className="mt-2">
                        {fn.isPending && <Loader2 className="size-4 animate-spin" />}
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
}
