import z from 'zod';

// Order Status Enum
export const OrderStatus = {
    PENDING: 'PENDING',
    CONFIRMED: 'CONFIRMED',
    SHIPPED: 'SHIPPED',
    DELIVERED: 'DELIVERED',
    CANCELLED: 'CANCELLED',
} as const;

export type OrderStatusType = typeof OrderStatus[keyof typeof OrderStatus];

// Payment Status Enum
export const PaymentStatus = {
    PENDING: 'PENDING',
    PAID: 'PAID',
    FAILED: 'FAILED',
    REFUNDED: 'REFUNDED',
} as const;

export type PaymentStatusType = typeof PaymentStatus[keyof typeof PaymentStatus];

// Order Update Status Schema
export const updateOrderStatusSchema = z.object({
    orderId: z.string().min(1, { message: 'Order ID is required' }),
    status: z.enum(['PENDING', 'CONFIRMED', 'SHIPPED', 'DELIVERED', 'CANCELLED']),
    paymentStatus: z.enum(['PENDING', 'PAID', 'FAILED', 'REFUNDED']),
});

export type UpdateOrderStatusSchema = z.infer<typeof updateOrderStatusSchema>;

// Get Order Schema
export const getOrderSchema = z.object({
    orderId: z.string().min(1, { message: 'Order ID is required' }),
});

export type GetOrderSchema = z.infer<typeof getOrderSchema>;

// Cancel Order Schema
export const cancelOrderSchema = z.object({
    orderId: z.string().min(1, { message: 'Order ID is required' }),
});

export type CancelOrderSchema = z.infer<typeof cancelOrderSchema>;

// Order Item Type
export type OrderItem = {
    _id: string;
    productId: {
        _id: string;
        name: string;
        description: string;
        sku: string;
        images: string[];
    };
    quantity: number;
    price: number;
    total: number;
};

// Order Type
export type Order = {
    _id: string;
    orderNumber: string;
    userId: {
        _id: string;
        name: string;
        email: string;
        phone: string;
        bankName: string;
        branchName: string;
    };
    items: OrderItem[];
    totalAmount: number;
    status: OrderStatusType;
    paymentStatus: PaymentStatusType;
    createdAt: string;
    updatedAt: string;
};

// Response Types
export type OrderListResponse = {
    success: boolean;
    message: string;
    data: {
        orders: Order[];
        total: number;
        limit: number;
        offset: number;
    };
};

export type OrderGetResponse = {
    success: boolean;
    message: string;
    data: {
        order: Order;
    };
};

export type OrderUpdateResponse = {
    success: boolean;
    message: string;
    data: {
        order: Order;
    };
};

export type OrderCancelResponse = {
    success: boolean;
    message: string;
    data: {
        order: Order;
    };
};
