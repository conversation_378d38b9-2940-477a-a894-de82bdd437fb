import z from 'zod';

// User Signup Schema
export const userSignupSchema = z.object({
    name: z.string().min(1, { message: 'Name is required' }),
    bankName: z.string().min(1, { message: 'Bank name is required' }),
    branchName: z.string().min(1, { message: 'Branch name is required' }),
    branchCode: z.string().min(1, { message: 'Branch code is required' }),
    phone: z.string().min(10, { message: 'Phone number must be at least 10 characters' }),
    email: z.string().email({ message: 'Please enter a valid email' }),
    adminId: z.string().min(1, { message: 'Admin ID is required' }),
});

export type UserSignupSchema = z.infer<typeof userSignupSchema>;

// User Login Schema
export const userLoginSchema = z.object({
    identifier: z.string().min(1, { message: 'Identifier is required' }), // Can be bank name or branch name
    password: z.string().min(1, { message: 'Password is required' }), // Branch code
});

export type UserLoginSchema = z.infer<typeof userLoginSchema>;

// User Types
export type User = {
    _id: string;
    name: string;
    bankName: string;
    branchName: string;
    branchCode: string;
    phone: string;
    email: string;
    adminId: string;
    status: string;
    createdAt: string;
    updatedAt: string;
};

export type UserTokens = {
    accessToken: string;
    refreshToken: string;
};

// Response Types
export type UserSignupResponse = {
    success: boolean;
    message: string;
    data: {
        user: User;
        tokens: UserTokens;
    };
};

export type UserLoginResponse = {
    success: boolean;
    message: string;
    data: {
        user: User;
        tokens: UserTokens;
    };
};

export type UserProfileResponse = {
    success: boolean;
    message: string;
    data: {
        user: User;
    };
};
