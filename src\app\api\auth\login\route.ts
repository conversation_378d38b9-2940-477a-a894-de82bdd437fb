import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { createSession } from '@/lib/session';
import { LoginResponse, loginSchema } from '@/modules/auth/schema';
import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const parsedBody = loginSchema.parse(body);
        console.log(parsedBody);
        const response: LoginResponse = (
            await axios({
                url: API_URL.admin.auth.login.url,
                method: API_URL.admin.auth.login.method,
                data: parsedBody,
            })
        ).data;
        console.log(response);
        await createSession({
            ...response.data.admin,
            ...response.data.tokens,
        });
        return NextResponse.json(response);
    } catch (error) {
        if (error instanceof Error) return NextResponse.json({ message: error.message }, { status: 500 });
        if (error instanceof AxiosError)
            return NextResponse.json({ message: error.response?.data?.message }, { status: 500 });
        if (error instanceof ZodError) return NextResponse.json({ message: error.message }, { status: 400 });
        return NextResponse.json({ message: 'Server error, please try again later.' }, { status: 500 });
    }
}
