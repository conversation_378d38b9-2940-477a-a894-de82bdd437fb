import { ApiPagination } from '@/types/global';
import z from 'zod';

export const baseProductSchema = z.object({
    _id: z.string().min(1, { message: 'is required' }),
    name: z.string().min(1, { message: 'name is required' }),
    description: z.string().min(1, { message: 'description is required' }),
    sku: z.string().min(1, { message: 'sku is required' }),
    images: z.array(z.string()),
    adminId: z.string().min(1, { message: 'adminId is required' }),
    status: z.string().min(1, { message: 'status is required' }),
    createdAt: z.string().min(1, { message: 'createdAt is required' }),
    updatedAt: z.string().min(1, { message: 'updatedAt is required' }),
});

export const createProductSchema = baseProductSchema.omit({
    _id: true,
    createdAt: true,
    updatedAt: true,
    adminId: true,
    status: true,
});

export const updateProductSchema = baseProductSchema
    .omit({
        _id: true,
        createdAt: true,
        updatedAt: true,
        adminId: true,
        status: true,
    })
    .extend({
        productId: z.string().min(1, { message: 'productId is required' }),
    });

export type ProductSchema = z.infer<typeof baseProductSchema>;
export type CreateProductSchema = z.infer<typeof createProductSchema>;
export type UpdateProductSchema = z.infer<typeof updateProductSchema>;

export type ProductListRespones = {
    success: boolean;
    data: {
        pagination: ApiPagination;
        products: ProductSchema[];
    };
};

export type ProductGetRespones = {
    success: boolean;
    data: {
        product: ProductSchema;
    };
};
