import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import React from 'react';

type ConfirmPropsBase = {
    title?: string;
    description?: string;
    onConfirm: () => void;
    variant?: React.ComponentProps<typeof Button>['variant'];
    children?: React.ReactNode;
};

type WithTrigger = ConfirmPropsBase & {
    trigger: React.ReactNode;
    open?: never;
    setOpen?: never;
};

type WithoutTrigger = ConfirmPropsBase & {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    trigger?: undefined;
};

type ConfirmProps = WithTrigger | WithoutTrigger;

export default function ConfirmDialog(props: ConfirmProps) {
    const {
        title = 'Are you sure?',
        description = 'This action cannot be undone.',
        variant = 'default',
        onConfirm,
    } = props;

    if ('trigger' in props && props.trigger) {
        return (
            <AlertDialog>
                <AlertDialogTrigger asChild>{props.trigger}</AlertDialogTrigger>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{title}</AlertDialogTitle>
                        <AlertDialogDescription>{description}</AlertDialogDescription>
                    </AlertDialogHeader>
                    {props.children ?? props.children}
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction variant={variant} onClick={onConfirm}>
                            Confirm
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        );
    }

    const { open, setOpen } = props as WithoutTrigger;
    return (
        <AlertDialog open={open} onOpenChange={setOpen}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{title}</AlertDialogTitle>
                    <AlertDialogDescription>{description}</AlertDialogDescription>
                </AlertDialogHeader>
                {props.children ?? props.children}
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={onConfirm}>Confirm</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
