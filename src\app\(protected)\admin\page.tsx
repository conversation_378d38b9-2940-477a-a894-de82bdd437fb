import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, IndianRupeeIcon, ShoppingCart, TrendingUp, Users } from 'lucide-react';

const dashboardStats = {
    totalUsers: 1247,
    pendingOrders: 23,
    completedOrders: 156,
    totalRevenue: 45280,
    monthlyGrowth: 12.5,
};

export default async function DashboardPage() {
    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-foreground text-3xl font-bold">Dashboard Overview</h1>
                <p className="text-muted-foreground mt-2">
                    Welcome to your business dashboard. Here&apos;s what&apos;s happening today.
                </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <Card className="gap-1">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-muted-foreground text-sm font-medium">Total Users</CardTitle>
                        <Users className="text-muted-foreground h-4 w-4" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-foreground text-2xl font-bold">
                            {dashboardStats.totalUsers.toLocaleString()}
                        </div>
                        <p className="text-muted-foreground mt-1 text-xs">Registered customers</p>
                    </CardContent>
                </Card>

                <Card className="gap-1">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-muted-foreground text-sm font-medium">Pending Orders</CardTitle>
                        <ShoppingCart className="text-muted-foreground h-4 w-4" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-foreground text-2xl font-bold">{dashboardStats.pendingOrders}</div>
                        <p className="text-muted-foreground mt-1 text-xs">Awaiting approval</p>
                    </CardContent>
                </Card>

                <Card className="gap-1">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-muted-foreground text-sm font-medium">Completed Orders</CardTitle>
                        <CheckCircle className="text-muted-foreground h-4 w-4" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-foreground text-2xl font-bold">{dashboardStats.completedOrders}</div>
                        <p className="text-muted-foreground mt-1 text-xs">This month</p>
                    </CardContent>
                </Card>

                <Card className="gap-1">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-muted-foreground text-sm font-medium">Total Revenue</CardTitle>
                        <IndianRupeeIcon className="text-muted-foreground h-4 w-4" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-foreground flex items-center text-2xl font-bold">
                            <IndianRupeeIcon className="mt-0.5 inline-block size-5" />
                            {dashboardStats.totalRevenue.toLocaleString()}
                        </div>
                        <div className="mt-1 flex items-center gap-1">
                            <TrendingUp className="h-3 w-3 text-green-600" />
                            <p className="text-xs text-green-600">+{dashboardStats.monthlyGrowth}% from last month</p>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <Card className="gap-3 p-3">
                <CardHeader className="px-0">
                    <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="px-0">
                    <div className="grid gap-4 md:grid-cols-3">
                        <div className="space-y-1 rounded-md border p-3">
                            <h3 className="text-foreground font-medium">Customer Management</h3>
                            <p className="text-muted-foreground text-sm">
                                View and manage your customer database, export reports, and update customer information.
                            </p>
                        </div>
                        <div className="space-y-1 rounded-md border p-3">
                            <h3 className="text-foreground font-medium">Order Processing</h3>
                            <p className="text-muted-foreground text-sm">
                                Review pending orders, approve or reject requests, and generate invoices for completed
                                orders.
                            </p>
                        </div>
                        <div className="space-y-1 rounded-md border p-3">
                            <h3 className="text-foreground font-medium">Analytics & Reports</h3>
                            <p className="text-muted-foreground text-sm">
                                Generate detailed reports, track performance metrics, and export data for analysis.
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
