import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { LayoutProps } from '@/types/global';

export default function DashboardLayout({ children }: LayoutProps) {
    return (
        <SidebarProvider>
            <AppSidebar />
            <SidebarInset className="h-[calc(100dvh-1rem)] overflow-hidden p-2">
                <div className="h-full overflow-y-auto">
                    <SidebarTrigger />
                    {children}
                </div>
            </SidebarInset>
        </SidebarProvider>
    );
}
