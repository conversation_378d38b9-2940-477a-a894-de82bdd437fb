import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { ProductUpdateResponse } from '@/modules/product/schema';
import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
    try {
        const formData = await req.formData();
        
        const response: ProductUpdateResponse = (
            await axios({
                url: API_URL.admin.product.update.url,
                method: API_URL.admin.product.update.method,
                data: formData,
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            })
        ).data;
        
        return NextResponse.json(response);
    } catch (error) {
        if (error instanceof Error) return NextResponse.json({ message: error.message }, { status: 500 });
        if (error instanceof AxiosError)
            return NextResponse.json({ message: error.response?.data?.message }, { status: 500 });
        return NextResponse.json({ message: 'Server error, please try again later.' }, { status: 500 });
    }
}
