import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { UserListResponse } from '@/modules/user/schema';
import { filterSchema } from '@/types/global';
import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const parsedBody = filterSchema.parse(body);
        
        const response: UserListResponse = (
            await axios({
                url: API_URL.admin.user.list.url,
                method: API_URL.admin.user.list.method,
                data: parsedBody,
            })
        ).data;
        
        return NextResponse.json(response);
    } catch (error) {
        if (error instanceof Error) return NextResponse.json({ message: error.message }, { status: 500 });
        if (error instanceof AxiosError)
            return NextResponse.json({ message: error.response?.data?.message }, { status: 500 });
        if (error instanceof ZodError) return NextResponse.json({ message: error.message }, { status: 400 });
        return NextResponse.json({ message: 'Server error, please try again later.' }, { status: 500 });
    }
}
