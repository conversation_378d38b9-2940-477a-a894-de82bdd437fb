{"info": {"_postman_id": "fe452a58-84af-4f42-baf4-ea3294bd1191", "name": "inkspot", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********"}, "item": [{"name": "users", "item": [{"name": "auth", "item": [{"name": "signup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"Gandhi Road\",\r\n    \"branchCode\": \"SBI00124\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"adminId\": \"68a9cf93343011ee3bf5237e\"\r\n  }\r\n  ", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/user/auth/signup", "host": ["{{BASE_URL}}"], "path": ["user", "auth", "signup"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "login", "event": [{"listen": "test", "script": {"exec": ["var responseBody = pm.response.json();\r", "var authToken = responseBody.data.tokens.accessToken;\r", "\r", "pm.environment.set(\"USER_TOKEN\", authToken);\r", "pm.environment.set(\"authToken\", authToken);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"identifier\": \"State Bank of India\", //MG Road \r\n    \"password\": \"SBI00123\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/user/auth/login", "host": ["{{BASE_URL}}"], "path": ["user", "auth", "login"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "profile", "event": [{"listen": "test", "script": {"exec": ["var responseBody = pm.response.json();\r", "var authToken = responseBody.data.tokens.accessToken;\r", "\r", "pm.environment.set(\"USER_TOKEN\", authToken);\r", "pm.environment.set(\"authToken\", authToken);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/user/auth/profile", "host": ["{{BASE_URL}}"], "path": ["user", "auth", "profile"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}]}, {"name": "product", "item": [{"name": "add", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Eraser", "type": "text"}, {"key": "description", "value": "This is a sample product.", "type": "text"}, {"key": "sku", "value": "SKU2", "type": "text"}, {"key": "images", "type": "file", "src": "/C:/Users/<USER>/Pictures/Screenshots/Screenshot (232).png"}]}, "url": {"raw": "{{BASE_URL}}/admin/product/create", "host": ["{{BASE_URL}}"], "path": ["admin", "product", "create"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"limit\": 100,\r\n    \"offset\": 0\r\n    // \"search\":\"eraser\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/user/product/list", "host": ["{{BASE_URL}}"], "path": ["user", "product", "list"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}]}, {"name": "cart", "item": [{"name": "add", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"productId\": \"68acacc472955c27f6113ab3\",\r\n  \"quantity\": 2\r\n}"}, "url": {"raw": "{{BASE_URL}}/user/cart/add", "host": ["{{BASE_URL}}"], "path": ["user", "cart", "add"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"limit\": 100,\r\n    \"offset\": 0\r\n    // \"search\":\"eraser\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/user/cart/list", "host": ["{{BASE_URL}}"], "path": ["user", "cart", "list"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"productId\": \"68b0a3e6c8c35b6b847f9116\",\r\n  \"quantity\": 3\r\n}"}, "url": {"raw": "{{BASE_URL}}/user/cart/update", "host": ["{{BASE_URL}}"], "path": ["user", "cart", "update"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "remove item", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"productId\": \"68b0a3e6c8c35b6b847f9116\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/user/cart/removeItem", "host": ["{{BASE_URL}}"], "path": ["user", "cart", "removeItem"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "clear", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{BASE_URL}}/user/cart/clear", "host": ["{{BASE_URL}}"], "path": ["user", "cart", "clear"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}]}, {"name": "order", "item": [{"name": "add", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{BASE_URL}}/user/order/add", "host": ["{{BASE_URL}}"], "path": ["user", "order", "add"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"limit\": 100,\r\n    \"offset\": 0\r\n    // \"search\":\"eraser\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/user/order/list", "host": ["{{BASE_URL}}"], "path": ["user", "order", "list"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"productId\": \"68b0a3e6c8c35b6b847f9116\",\r\n  \"quantity\": 3\r\n}"}, "url": {"raw": "{{BASE_URL}}/user/cart/update", "host": ["{{BASE_URL}}"], "path": ["user", "cart", "update"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "remove item", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"productId\": \"68b0a3e6c8c35b6b847f9116\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/user/cart/removeItem", "host": ["{{BASE_URL}}"], "path": ["user", "cart", "removeItem"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "clear", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{USER_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{BASE_URL}}/user/cart/clear", "host": ["{{BASE_URL}}"], "path": ["user", "cart", "clear"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}]}]}, {"name": "admin", "item": [{"name": "admin", "item": [{"name": "add", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"<PERSON>\",\r\n  \"username\": \"alice.admin\",\r\n  \"password\": \"123456\",\r\n  \"phone\": \"+************\",\r\n  \"email\": \"<EMAIL>\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{DEV_URL}}/admin/auth/create", "host": ["{{DEV_URL}}"], "path": ["admin", "auth", "create"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "login", "event": [{"listen": "test", "script": {"exec": ["var responseBody = pm.response.json();\r", "var authToken = responseBody.data.tokens.accessToken;\r", "\r", "pm.environment.set(\"ADMIN_TOKEN\", authToken);\r", "pm.environment.set(\"authToken\", authToken);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"password\": \"123456\",\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/admin/auth/login", "host": ["{{BASE_URL}}"], "path": ["admin", "auth", "login"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}]}, {"name": "order", "item": [{"name": "List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"limit\": 100,\r\n    \"offset\": 0,\r\n    \"search\":\"*************\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/admin/order/list", "host": ["{{BASE_URL}}"], "path": ["admin", "order", "list"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "update status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"orderId\": \"68b359037eb284a6bf7d007c\",\r\n    \"status\": \"CONFIRMED\", //'PENDING', 'CONFIRMED', 'SHIPPED', 'DELIVERED', 'CANCELLED'\r\n    \"paymentStatus\":\"PAID\" //'PENDING', 'PAID', 'FAILED', 'REFUNDED'\r\n}"}, "url": {"raw": "{{BASE_URL}}/admin/order/update-status", "host": ["{{BASE_URL}}"], "path": ["admin", "order", "update-status"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "get", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"orderId\": \"68b359037eb284a6bf7d007c\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/admin/order/get", "host": ["{{BASE_URL}}"], "path": ["admin", "order", "get"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "cancel", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"orderId\": \"68b359037eb284a6bf7d007c\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/admin/order/cancel", "host": ["{{BASE_URL}}"], "path": ["admin", "order", "cancel"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}]}, {"name": "user", "item": [{"name": "List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"limit\": 100,\r\n    \"offset\": 0\r\n}"}, "url": {"raw": "{{BASE_URL}}/admin/user/list", "host": ["{{BASE_URL}}"], "path": ["admin", "user", "list"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}]}, {"name": "product", "item": [{"name": "add", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Eraser", "type": "text"}, {"key": "description", "value": "This is a sample product.", "type": "text"}, {"key": "sku", "value": "SKU2", "type": "text"}, {"key": "images", "type": "file", "src": "/C:/Users/<USER>/Pictures/Screenshots/Screenshot (232).png"}]}, "url": {"raw": "{{BASE_URL}}/admin/product/create", "host": ["{{BASE_URL}}"], "path": ["admin", "product", "create"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{BASE_URL}}/admin/product/list", "host": ["{{BASE_URL}}"], "path": ["admin", "product", "list"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"productId\": \"68b09621f623647b3bf685bd\",\r\n    \"name\": \"Updated Pen\",\r\n    \"description\": \"Blue ink gel pen - updated\",\r\n    \"sku\": \"PEN1234\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/admin/product/update", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "product", "update"]}, "description": "Generated from cURL: curl --location 'http://localhost:3000/api/product/getProductById' \\\r\n--header 'Content-Type: application/json' \\\r\n--data '{\r\n  \"productId\": \"68b09621f623647b3bf685bd\"\r\n}'\r\n"}, "response": []}, {"name": "delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"productId\": \"68b09621f623647b3bf685bd\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/admin/product/delete", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "product", "delete"]}, "description": "Generated from cURL: curl --location 'http://localhost:3000/api/product/getProductById' \\\r\n--header 'Content-Type: application/json' \\\r\n--data '{\r\n  \"productId\": \"68b09621f623647b3bf685bd\"\r\n}'\r\n"}, "response": []}, {"name": "get", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"productId\": \"68b09621f623647b3bf685bd\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/admin/product/get", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "product", "get"]}, "description": "Generated from cURL: curl --location 'http://localhost:3000/api/product/getProductById' \\\r\n--header 'Content-Type: application/json' \\\r\n--data '{\r\n  \"productId\": \"68b09621f623647b3bf685bd\"\r\n}'\r\n"}, "response": []}, {"name": "image delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"productId\": \"68b09621f623647b3bf685bd\",\r\n    \"imageUrl\": \"https://stationery-backend-3abw.onrender.com/uploads/images-1756403233238-620372048.jpg\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{DEV_URL}}/admin/product/delete-image", "host": ["{{DEV_URL}}"], "path": ["admin", "product", "delete-image"]}, "description": "Generated from cURL: curl --location 'http://localhost:3000/api/product/getProductById' \\\r\n--header 'Content-Type: application/json' \\\r\n--data '{\r\n  \"productId\": \"68b09621f623647b3bf685bd\"\r\n}'\r\n"}, "response": []}]}, {"name": "inventories", "item": [{"name": "add", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"productId\": \"68acafee8dbe20aee5b5c86b\",\r\n  \"price\": 120.5,\r\n  \"quantity\": 50\r\n}\r\n"}, "url": {"raw": "{{BASE_URL}}/admin/inventory/create", "host": ["{{BASE_URL}}"], "path": ["admin", "inventory", "create"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{BASE_URL}}/admin/inventory/list", "host": ["{{BASE_URL}}"], "path": ["admin", "inventory", "list"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "get", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inventoryId\": \"68b08a6ea09adcad65b5ba28\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/admin/inventory/get", "host": ["{{BASE_URL}}"], "path": ["admin", "inventory", "get"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inventoryId\": \"68b08a6ea09adcad65b5ba28\",\r\n    \"productId\": \"68acacc472955c27f6113ab3\",\r\n    \"price\": 120.5,\r\n    \"quantity\": 50\r\n}"}, "url": {"raw": "{{BASE_URL}}/admin/inventory/update", "host": ["{{BASE_URL}}"], "path": ["admin", "inventory", "update"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "bulk update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\r\n    {\r\n        \"inventoryId\": \"68b08a6ea09adcad65b5ba28\",\r\n        \"productId\": \"68acacc472955c27f6113ab3\",\r\n        \"price\": 120.5,\r\n        \"quantity\": 50\r\n    }\r\n]"}, "url": {"raw": "{{BASE_URL}}/admin/inventory/bulk-update", "host": ["{{BASE_URL}}"], "path": ["admin", "inventory", "bulk-update"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}, {"name": "DELETED", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inventoryId\": \"68b08a6ea09adcad65b5ba28\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/admin/inventory/delete", "host": ["{{BASE_URL}}"], "path": ["admin", "inventory", "delete"]}, "description": "Generated from cURL: curl -X POST http://localhost:3000/api/users/signup \\\r\n  -H \"Content-Type: application/json\" \\\r\n  -d '{\r\n    \"name\": \"<PERSON>\",\r\n    \"bankName\": \"State Bank of India\",\r\n    \"branchName\": \"MG Road\",\r\n    \"branchCode\": \"SBI00123\",\r\n    \"phone\": \"+************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"SecurePass123\",\r\n    \"adminId\": \"64d2f29a1a4b8c9f2e123456\"\r\n  }'\r\n"}, "response": []}]}]}]}