import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { ProductGetRespones, ProductListRespones } from '@/modules/product/schema';
import { FilterSchema } from '@/types/global';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const productList = async (data: FilterSchema): Promise<ProductListRespones> => {
    return (await axios.post(API_URL.admin.product.list.url, data)).data;
};

export const useProducts = (data: Partial<FilterSchema>) => {
    const { limit = 10, offset = 0, search } = data;
    return useQuery({
        queryKey: ['product', data],
        queryFn: async () => await productList({ limit, offset, search }),
    });
};

export const getProduct = async (data: { productId: string }): Promise<ProductGetRespones> => {
    return (await axios.post(API_URL.admin.product.get.url, data)).data;
};

export const useProduct = (productId: string) => {
    const enabled = productId !== 'create' && productId !== 'add' && !!productId;
    return useQuery({
        queryKey: ['product', productId],
        queryFn: async () => await getProduct({ productId }),
        enabled,
    });
};

export const createProduct = async (data: FormData) => {
    return (await axios.post(API_URL.admin.product.create.url, data)).data;
};

export const useCreateProduct = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createProduct,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['product'],
            });
        },
    });
};

export const updateProduct = async (data: FormData) => {
    return (await axios.post('/api/admin/product/update', data)).data;
};

export const useUpdateProduct = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: updateProduct,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['product'],
            });
        },
    });
};

export const deleteProductImage = async (data: { productId: string; imageUrl: string }) => {
    return (await axios.post(API_URL.admin.product['delete-image'].url, data)).data;
};

export const useDeleteProductImage = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: deleteProductImage,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['product'] });
        },
    });
};

export const deleteProduct = async (data: { productId: string }) => {
    return (await axios.post(API_URL.admin.product.delete.url, data)).data;
};

export const useDeleteProduct = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: deleteProduct,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['product'] });
        },
    });
};
