import { AppSidebar } from '@/components/app-sidebar';
import LumaSpin from '@/components/luma-spin';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

export default function DashboardLoader() {
    return (
        <SidebarProvider>
            <AppSidebar />
            <SidebarInset className="h-[calc(100dvh-1rem)] overflow-hidden p-2">
                <div className="h-full overflow-y-auto">
                    <div className="flex size-full items-center justify-center">
                        <LumaSpin />
                    </div>
                </div>
            </SidebarInset>
        </SidebarProvider>
    );
}
