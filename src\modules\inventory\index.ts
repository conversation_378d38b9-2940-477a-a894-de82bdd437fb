import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { InventoryGetResponse, InventoryListResponse, UpdateInventorySchema } from '@/modules/inventory/schema';
import { FilterSchema } from '@/types/global';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const inventoryList = async (data: FilterSchema): Promise<InventoryListResponse> => {
    return (await axios.post(API_URL.admin.inventory.list.url, data)).data;
};
export const useInventory = (data: Partial<FilterSchema>) => {
    const { limit = 10, offset = 0, search } = data;
    return useQuery({
        queryKey: ['inventory', data],
        queryFn: async () => await inventoryList({ limit, offset, search }),
    });
};

export const getInventory = async (data: { inventoryId: string }): Promise<InventoryGetResponse> => {
    return (await axios.post(API_URL.admin.inventory.get.url, data)).data;
};
export const useGetInventory = (inventoryId: string) => {
    const enabled = inventoryId !== 'create' && inventoryId !== 'add' && !!inventoryId;
    return useQuery({
        queryKey: ['inventory', 'get', inventoryId],
        queryFn: async () => await getInventory({ inventoryId }),
        enabled,
    });
};

export const createInventory = async (data: FormData) => {
    return (await axios.post(API_URL.admin.inventory.create.url, data)).data;
};
export const useCreateInventory = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createInventory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['inventory'] });
        },
    });
};

export const updateInventory = async (data: UpdateInventorySchema) => {
    return (await axios.post(API_URL.admin.inventory.update.url, data)).data;
};
export const useUpdateInventory = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: updateInventory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['inventory'] });
        },
    });
};

export const bulkUpdateInventory = async (data: any) => {
    return (await axios.post(API_URL.admin.inventory['bulk-update'].url, data)).data;
};
export const useBulkUpdateInventory = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: bulkUpdateInventory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['inventory'] });
        },
    });
};

export const deleteInventory = async (data: { inventoryId: string }) => {
    return (await axios.post(API_URL.admin.inventory.delete.url, data)).data;
};

export const useDeleteInventory = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: deleteInventory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['inventory'] });
        },
    });
};
