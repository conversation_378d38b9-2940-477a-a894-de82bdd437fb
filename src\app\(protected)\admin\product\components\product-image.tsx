'use client';
import DeleteDialog from '@/components/delete-dialog';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { BASE_URL } from '@/constants';
import { useDeleteProductImage } from '@/modules/product';
import { ProductSchema } from '@/modules/product/schema';
import { EyeIcon, Loader2, XIcon } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';

type ProductImageProps = {
    src: string;
    product: ProductSchema;
};

export default function ProductImage({ src, product }: ProductImageProps) {
    const [deleteDialog, setDeleteDialog] = useState(false);
    const [open, setOpen] = useState(false);
    const removeProductImage = useDeleteProductImage();
    src = src.startsWith('https') ? src : `${BASE_URL}${src}`;
    const handleConfirm = () => {
        removeProductImage.mutate(
            { productId: product._id, imageUrl: src },
            {
                onSuccess: () => {
                    setDeleteDialog(false);
                },
            },
        );
    };
    return (
        <>
            <div className="group relative aspect-square overflow-hidden rounded border">
                <div className="relative aspect-square size-full">
                    <Image fill src={src} alt={product.name} className="h-full w-full object-cover" />
                </div>
                <div className="absolute inset-0 flex flex-col items-end justify-end gap-1 p-1 opacity-0 transition-opacity group-hover:opacity-100">
                    <Button type="button" size="icon" variant="outline" className="mb-1" onClick={() => setOpen(true)}>
                        <EyeIcon className="size-4" />
                    </Button>
                    <Button
                        type="button"
                        size="icon"
                        variant="destructive"
                        disabled={removeProductImage.isPending}
                        onClick={() => setDeleteDialog(true)}
                    >
                        {removeProductImage.isPending ? (
                            <Loader2 className="size-4 animate-spin" />
                        ) : (
                            <XIcon className="size-4" />
                        )}
                    </Button>
                </div>
            </div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="max-w-lg">
                    <DialogTitle>{product.name}</DialogTitle>
                    <div className="relative h-72 w-full">
                        <Image src={src} alt={product.name} fill className="object-contain" sizes="100vw" />
                    </div>
                </DialogContent>
            </Dialog>
            <DeleteDialog open={deleteDialog} setOpen={setDeleteDialog} onConfirm={handleConfirm} />
        </>
    );
}
