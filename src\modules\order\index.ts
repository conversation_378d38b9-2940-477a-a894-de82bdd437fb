import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { 
    OrderListResponse, 
    OrderGetResponse, 
    OrderUpdateResponse, 
    OrderCancelResponse,
    UpdateOrderStatusSchema,
    GetOrderSchema,
    CancelOrderSchema
} from '@/modules/order/schema';
import { FilterSchema } from '@/types/global';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

// Order List
export const orderList = async (data: FilterSchema): Promise<OrderListResponse> => {
    return (await axios.post(API_URL.admin.order.list.url, data)).data;
};

export const useOrders = (data: Partial<FilterSchema>) => {
    const { limit = 10, offset = 0, search } = data;
    return useQuery({
        queryKey: ['orders', data],
        queryFn: async () => await orderList({ limit, offset, search }),
    });
};

// Get Order
export const getOrder = async (data: GetOrderSchema): Promise<OrderGetResponse> => {
    return (await axios.post(API_URL.admin.order.get.url, data)).data;
};

export const useOrder = (orderId: string) => {
    const enabled = !!orderId;
    return useQuery({
        queryKey: ['order', orderId],
        queryFn: async () => await getOrder({ orderId }),
        enabled,
    });
};

// Update Order Status
export const updateOrderStatus = async (data: UpdateOrderStatusSchema): Promise<OrderUpdateResponse> => {
    return (await axios.post(API_URL.admin.order['update-status'].url, data)).data;
};

export const useUpdateOrderStatus = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: updateOrderStatus,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['orders'] });
            queryClient.invalidateQueries({ queryKey: ['order'] });
        },
    });
};

// Cancel Order
export const cancelOrder = async (data: CancelOrderSchema): Promise<OrderCancelResponse> => {
    return (await axios.post(API_URL.admin.order.cancel.url, data)).data;
};

export const useCancelOrder = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: cancelOrder,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['orders'] });
            queryClient.invalidateQueries({ queryKey: ['order'] });
        },
    });
};
