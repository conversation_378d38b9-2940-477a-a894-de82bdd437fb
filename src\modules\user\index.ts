import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { UserListResponse } from '@/modules/user/schema';
import { FilterSchema } from '@/types/global';
import { useQuery } from '@tanstack/react-query';

// User List
export const userList = async (data: FilterSchema): Promise<UserListResponse> => {
    return (await axios.post(API_URL.admin.user.list.url, data)).data;
};

export const useUsers = (data: Partial<FilterSchema>) => {
    const { limit = 10, offset = 0, search } = data;
    return useQuery({
        queryKey: ['users', data],
        queryFn: async () => await userList({ limit, offset, search }),
    });
};
