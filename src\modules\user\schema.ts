import z from 'zod';

// User Type
export type User = {
    _id: string;
    name: string;
    bankName: string;
    branchName: string;
    branchCode: string;
    phone: string;
    email: string;
    adminId: string;
    status: string;
    createdAt: string;
    updatedAt: string;
};

// Response Types
export type UserListResponse = {
    success: boolean;
    message: string;
    data: {
        users: User[];
        total: number;
        limit: number;
        offset: number;
    };
};
