'use client';

import { Bad<PERSON> } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Check, Eye, FileText, Search, X } from 'lucide-react';
import { useState } from 'react';

interface Order {
    id: string;
    user: string;
    items: string;
    amount: number;
    status: 'pending' | 'approved' | 'rejected' | 'completed';
    date: string;
}

// Mock order data
const initialOrders: Order[] = [
    {
        id: 'ORD-001',
        user: '<PERSON>',
        items: 'La<PERSON><PERSON>, Mouse, Keyboard',
        amount: 1299.99,
        status: 'pending',
        date: '2024-01-15',
    },
    {
        id: 'ORD-002',
        user: '<PERSON>',
        items: 'Smartphone, Case',
        amount: 899.5,
        status: 'approved',
        date: '2024-01-14',
    },
    {
        id: 'ORD-003',
        user: '<PERSON>',
        items: 'Tablet, <PERSON>ylus',
        amount: 599.99,
        status: 'pending',
        date: '2024-01-13',
    },
    {
        id: 'ORD-004',
        user: '<PERSON> <PERSON>',
        items: 'Headphones, Charger',
        amount: 199.99,
        status: 'completed',
        date: '2024-01-12',
    },
    {
        id: 'ORD-005',
        user: 'David Brown',
        items: 'Monitor, HDMI Cable',
        amount: 449.99,
        status: 'rejected',
        date: '2024-01-11',
    },
    {
        id: 'ORD-006',
        user: 'Lisa Anderson',
        items: 'Webcam, Microphone',
        amount: 299.99,
        status: 'approved',
        date: '2024-01-10',
    },
];

export default function OrdersPage() {
    const [orders, setOrders] = useState<Order[]>(initialOrders);
    const [filteredOrders, setFilteredOrders] = useState<Order[]>(initialOrders);
    const [searchTerm, setSearchTerm] = useState('');

    // Filter orders based on search term
    const handleSearch = (term: string) => {
        setSearchTerm(term);
        if (term === '') {
            setFilteredOrders(orders);
        } else {
            const filtered = orders.filter(
                (order) =>
                    order.id.toLowerCase().includes(term.toLowerCase()) ||
                    order.user.toLowerCase().includes(term.toLowerCase()) ||
                    order.items.toLowerCase().includes(term.toLowerCase()) ||
                    order.status.toLowerCase().includes(term.toLowerCase()),
            );
            setFilteredOrders(filtered);
        }
    };

    // Handle order approval
    const handleApproveOrder = (orderId: string) => {
        const updatedOrders = orders.map((order) =>
            order.id === orderId ? { ...order, status: 'approved' as const } : order,
        );
        setOrders(updatedOrders);
        setFilteredOrders(updatedOrders);
    };

    // Handle order rejection
    const handleRejectOrder = (orderId: string) => {
        const updatedOrders = orders.map((order) =>
            order.id === orderId ? { ...order, status: 'rejected' as const } : order,
        );
        setOrders(updatedOrders);
        setFilteredOrders(updatedOrders);
    };

    // Handle create invoice
    const handleCreateInvoice = (invoice: Order) => {
        const updatedOrders = orders.map((order) =>
            order.id === invoice?.id ? { ...order, status: 'completed' as const } : order,
        );
        setOrders(updatedOrders);
        setFilteredOrders(updatedOrders);
        alert(`Invoice ${invoice.id} created successfully!`);
    };

    const getStatusTheme = (status: Order['status']) => {
        switch (status) {
            case 'pending':
                return 'text-yellow-600 bg-yellow-50 border border-yellow-600';
            case 'approved':
                return 'text-blue-600 bg-blue-50 border border-blue-600';
            case 'completed':
                return 'text-green-600 bg-green-50 border border-green-600';
            case 'rejected':
                return 'text-red-600 bg-red-50 border border-red-600';
            default:
                return 'text-gray-600 bg-gray-50 border border-gray-600';
        }
    };

    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-foreground text-3xl font-bold">Orders</h1>
                <p className="text-muted-foreground mt-2">
                    Review and manage customer orders, approve requests, and generate invoices.
                </p>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Order Management</CardTitle>
                    <div className="flex items-center justify-between gap-4">
                        <div className="relative max-w-sm flex-1">
                            <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
                            <Input
                                placeholder="Search orders..."
                                value={searchTerm}
                                onChange={(e) => handleSearch(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                        <div className="flex gap-2">
                            <Badge className={getStatusTheme('pending')}>
                                {orders.filter((o) => o.status === 'pending').length} Pending
                            </Badge>
                            <Badge className={getStatusTheme('approved')}>
                                {orders.filter((o) => o.status === 'approved').length} Approved
                            </Badge>
                            <Badge className={getStatusTheme('completed')}>
                                {orders.filter((o) => o.status === 'completed').length} Completed
                            </Badge>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Order ID</TableHead>
                                    <TableHead>User</TableHead>
                                    <TableHead>Items</TableHead>
                                    <TableHead>Amount</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Date</TableHead>
                                    <TableHead className="text-right">Action</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredOrders.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={7} className="text-muted-foreground py-8 text-center">
                                            No orders found
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    filteredOrders.map((order) => (
                                        <TableRow key={order.id}>
                                            <TableCell className="font-medium">{order.id}</TableCell>
                                            <TableCell>{order.user}</TableCell>
                                            <TableCell className="max-w-xs truncate" title={order.items}>
                                                {order.items}
                                            </TableCell>
                                            <TableCell>${order.amount.toFixed(2)}</TableCell>
                                            <TableCell>
                                                <Badge className={getStatusTheme(order.status)}>
                                                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>{order.date}</TableCell>
                                            <TableCell className="text-right">
                                                {order.status === 'pending' ? (
                                                    <div className="flex justify-end gap-1">
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handleApproveOrder(order.id)}
                                                            className="h-8 gap-1 text-green-600 hover:text-green-700"
                                                        >
                                                            <Check className="h-3 w-3" />
                                                            Approve
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handleRejectOrder(order.id)}
                                                            className="h-8 gap-1 text-red-600 hover:text-red-700"
                                                        >
                                                            <X className="h-3 w-3" />
                                                            Reject
                                                        </Button>
                                                    </div>
                                                ) : order.status === 'approved' ? (
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={() => handleCreateInvoice(order)}
                                                        className="h-8 gap-1"
                                                    >
                                                        <FileText className="h-3 w-3" />
                                                        Create Invoice
                                                    </Button>
                                                ) : order.status === 'completed' ? (
                                                    <Button
                                                        size="sm"
                                                        variant="ghost"
                                                        className="text-muted-foreground h-8 gap-1"
                                                    >
                                                        <Eye className="h-3 w-3" />
                                                        View Invoice
                                                    </Button>
                                                ) : (
                                                    <span className="text-muted-foreground text-sm">No actions</span>
                                                )}
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>

                    {filteredOrders.length > 0 && (
                        <div className="mt-4 flex items-center justify-between">
                            <p className="text-muted-foreground text-sm">
                                Showing {filteredOrders.length} of {orders.length} orders
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
