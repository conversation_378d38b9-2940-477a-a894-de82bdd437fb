import { cn } from '@/lib/utils';
import QueryProvider from '@/providers/query';
import { LayoutProps } from '@/types/global';
import type { Metadata } from 'next';
import { Geist, Geist_Mono } from 'next/font/google';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { Toaster } from 'sonner';
import './globals.css';

const geistSans = Geist({
    variable: '--font-geist-sans',
    subsets: ['latin'],
});

const geistMono = Geist_Mono({
    variable: '--font-geist-mono',
    subsets: ['latin'],
});

export const metadata: Metadata = {
    title: 'Create Next App',
    description: 'Generated by create next app',
};

export default function RootLayout({ children }: LayoutProps) {
    return (
        <html lang="en">
            <body className={cn(geistSans.variable, geistMono.variable, 'antialiased')}>
                <NuqsAdapter>
                    <QueryProvider>
                        {children}
                        <Toaster richColors closeButton />
                    </QueryProvider>
                </NuqsAdapter>
            </body>
        </html>
    );
}
