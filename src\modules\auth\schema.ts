import z from 'zod';

export const loginSchema = z.object({
    email: z.string().email({ message: 'Please enter a valid email' }),
    password: z.string().min(1, { message: 'Password must be at least 1 characters' }),
});

export type LoginSchema = z.infer<typeof loginSchema>;

export type Admin = {
    _id: string;
    name: string;
    username: string;
    email: string;
    phone: string;
    status: string;
};

export type Tokens = {
    accessToken: string;
    refreshToken: string;
};

export type LoginResponse = {
    success: boolean;
    message: string;
    data: {
        admin: {
            _id: string;
            name: string;
            username: string;
            email: string;
            phone: string;
            status: string;
        };
        tokens: Tokens;
    };
};
