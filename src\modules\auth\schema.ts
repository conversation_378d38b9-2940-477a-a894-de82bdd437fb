import z from 'zod';

export const loginSchema = z.object({
    email: z.string().email({ message: 'Please enter a valid email' }),
    password: z.string().min(1, { message: 'Password must be at least 1 characters' }),
});

export type LoginSchema = z.infer<typeof loginSchema>;

export const createAdminSchema = z.object({
    name: z.string().min(1, { message: 'Name is required' }),
    username: z.string().min(1, { message: 'Username is required' }),
    password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
    phone: z.string().min(10, { message: 'Phone number must be at least 10 characters' }),
    email: z.string().email({ message: 'Please enter a valid email' }),
});

export type CreateAdminSchema = z.infer<typeof createAdminSchema>;

export type Admin = {
    _id: string;
    name: string;
    username: string;
    email: string;
    phone: string;
    status: string;
};

export type Tokens = {
    accessToken: string;
    refreshToken: string;
};

export type LoginResponse = {
    success: boolean;
    message: string;
    data: {
        admin: {
            _id: string;
            name: string;
            username: string;
            email: string;
            phone: string;
            status: string;
        };
        tokens: Tokens;
    };
};

export type CreateAdminResponse = {
    success: boolean;
    message: string;
    data: {
        admin: Admin;
        tokens: Tokens;
    };
};
