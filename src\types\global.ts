import z from 'zod';

export type LayoutProps = Readonly<{
    children: React.ReactNode;
}>;

export type ApiPagination = {
    limit: number;
    offset: number;
    total: number;
};

export const filterSchema = z.object({
    limit: z.number().optional().default(10),
    offset: z.number().optional().default(0),
    search: z.string().optional(),
});

export type FilterSchema = z.infer<typeof filterSchema>;
