import { baseProductSchema } from '@/modules/product/schema';
import { ApiPagination } from '@/types/global';
import * as z from 'zod';

const inventoryProduct = baseProductSchema.pick({
    _id: true,
    name: true,
    sku: true,
    images: true,
    status: true,
});

export const baseInventorySchema = z.object({
    _id: z.string(),
    sku: z.string(),
    price: z.coerce.number().positive(),
    quantity: z.coerce.number().positive(),
    adminId: z.string(),
    productId: z.string().min(1, { message: 'Product Id is required' }),
    product: inventoryProduct,
    status: z.enum(['ACTIVE', 'INACTIVE']),
    createdAt: z.string().min(1, { message: 'createdAt is required' }),
    updatedAt: z.string().min(1, { message: 'updatedAt is required' }),
});

export const createInventorySchema = baseInventorySchema.omit({
    _id: true,
    sku: true,
    createdAt: true,
    updatedAt: true,
    adminId: true,
    status: true,
    product: true,
});

export const updateInventorySchema = z.intersection(
    z.object({
        inventoryId: z.string().min(1, { message: 'inventoryId is required' }),
    }),
    baseInventorySchema
        .omit({
            createdAt: true,
            updatedAt: true,
            sku: true,
            adminId: true,
        })
        .partial(),
);

export type InventorySchema = z.infer<typeof baseInventorySchema>;
export type CreateInventorySchema = z.infer<typeof createInventorySchema>;
export type UpdateInventorySchema = z.infer<typeof updateInventorySchema>;

export type InventoryListResponse = {
    success: boolean;
    data: {
        pagination: ApiPagination;
        inventory: InventorySchema[];
    };
};

export type InventoryGetResponse = {
    success: boolean;
    data: {
        inventory: InventorySchema;
    };
};

// Additional schemas for API endpoints
export const getInventorySchema = z.object({
    inventoryId: z.string().min(1, { message: 'Inventory ID is required' }),
});

export const deleteInventorySchema = z.object({
    inventoryId: z.string().min(1, { message: 'Inventory ID is required' }),
});

export const bulkUpdateInventorySchema = z.array(updateInventorySchema);

export type GetInventorySchema = z.infer<typeof getInventorySchema>;
export type DeleteInventorySchema = z.infer<typeof deleteInventorySchema>;
export type BulkUpdateInventorySchema = z.infer<typeof bulkUpdateInventorySchema>;

export type InventoryCreateResponse = {
    success: boolean;
    message: string;
    data: {
        inventory: InventorySchema;
    };
};

export type InventoryUpdateResponse = {
    success: boolean;
    message: string;
    data: {
        inventory: InventorySchema;
    };
};

export type InventoryDeleteResponse = {
    success: boolean;
    message: string;
    data?: any;
};

export type InventoryBulkUpdateResponse = {
    success: boolean;
    message: string;
    data: {
        inventory: InventorySchema[];
    };
};
