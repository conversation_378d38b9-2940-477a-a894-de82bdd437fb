import { verifySession } from '@/lib/session';
import { createRouteMatcher } from '@/lib/utils';
import { NextRequest, NextResponse } from 'next/server';

const privateRoutes = createRouteMatcher(['/admin', '/admin/(.*)']);
const authRoutes = createRouteMatcher(['/auth/login', '/auth/register', '/']);

export async function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;
    const isPrivateRoute = privateRoutes(pathname);
    const isAuthRoute = authRoutes(pathname);
    const isAuth = await verifySession();
    if (!isAuth && isPrivateRoute) return NextResponse.redirect(new URL('/auth/login', request.url));
    if (isAuthRoute && isAuth) return NextResponse.redirect(new URL('/admin', request.url));
    return NextResponse.next();
}

export const config = {
    matcher: [
        '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
        '/(api|trpc)(.*)',
    ],
};
