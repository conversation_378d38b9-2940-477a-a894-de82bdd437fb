'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { useDeleteInventory, useUpdateInventory } from '@/modules/inventory';
import { InventorySchema } from '@/modules/inventory/schema';
import { CellContext, ColumnDef } from '@tanstack/react-table';
import { EditIcon, ImageIcon, Loader2, Trash2Icon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

export const columns: ColumnDef<InventorySchema>[] = [
    {
        id: 'images',
        header: () => <ImageIcon className="size-4" strokeWidth={1.5} />,
        cell: InventoryImages,
    },
    {
        id: 'name',
        header: 'Name',
        accessorFn: (row) => row.product.name,
    },
    {
        id: 'sku',
        header: 'SKU',
        accessorFn: (row) => row.product.sku,
    },
    {
        id: 'price',
        header: 'Price',
        accessorFn: (row) => row.price,
        cell: ({ row }) => <span>${row.original.price}</span>,
    },
    {
        id: 'quantity',
        header: 'Quantity',
        accessorFn: (row) => row.quantity,
    },
    {
        id: 'status',
        header: 'Status',
        cell: InventoryStatus,
    },
    {
        id: 'id',
        header: '',
        cell: InventoryAction,
    },
];

function InventoryImages({ row }: CellContext<InventorySchema, unknown>) {
    const productName = row.original.product.name;
    const productImages: string[] = row.original.product.images || [];
    const [open, setOpen] = useState(false);

    if (!productImages.length) return <ImageIcon className="size-9.5" strokeWidth={1} />;

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <div className="relative flex aspect-square size-10 cursor-pointer gap-1">
                    {productImages.slice(0, 1).map((src: string, i: number) => (
                        <div key={i} className="relative size-full overflow-hidden rounded border">
                            <Image src={src} alt={productName} fill className="object-cover" />
                        </div>
                    ))}

                    {productImages.length > 1 && (
                        <Badge className="border-background absolute -top-1.5 left-full min-w-5 -translate-x-3.5 px-1">
                            {productImages.length - 1}
                        </Badge>
                    )}
                </div>
            </DialogTrigger>
            <DialogContent className="max-w-lg">
                <DialogTitle>{productName}</DialogTitle>
                <Carousel>
                    <CarouselContent>
                        {productImages.map((src: string, i: number) => (
                            <CarouselItem key={i}>
                                <div className="relative h-72 w-full">
                                    <Image src={src} alt={productName} fill className="object-contain" sizes="100vw" />
                                </div>
                            </CarouselItem>
                        ))}
                    </CarouselContent>
                    <CarouselPrevious />
                    <CarouselNext />
                </Carousel>
            </DialogContent>
        </Dialog>
    );
}

function InventoryStatus({ row }: CellContext<InventorySchema, unknown>) {
    const isActive = row.original.status === 'ACTIVE';
    const updateInventory = useUpdateInventory();
    return (
        <Switch
            disabled={updateInventory.isPending}
            checked={isActive}
            onCheckedChange={() =>
                updateInventory.mutate({ inventoryId: row.original._id, status: isActive ? 'INACTIVE' : 'ACTIVE' })
            }
        />
    );
}

function InventoryAction({ row }: CellContext<InventorySchema, unknown>) {
    const deleteInventory = useDeleteInventory();
    return (
        <div className="flex gap-1.5">
            <Button variant="outline" size="icon" asChild>
                <Link href={`/admin/inventory/${row.original._id}`}>
                    <EditIcon className="size-4" />
                </Link>
            </Button>
            <Button
                onClick={() => deleteInventory.mutate({ inventoryId: row.original._id })}
                disabled={deleteInventory.isPending}
                variant="destructive"
                size="icon"
            >
                {deleteInventory.isPending ? (
                    <Loader2 className="size-4 animate-spin" />
                ) : (
                    <Trash2Icon className="size-4" />
                )}
            </Button>
        </div>
    );
}
