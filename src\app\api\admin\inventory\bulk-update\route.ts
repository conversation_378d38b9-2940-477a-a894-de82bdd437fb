import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { InventoryBulkUpdateResponse, bulkUpdateInventorySchema } from '@/modules/inventory/schema';
import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const parsedBody = bulkUpdateInventorySchema.parse(body);
        
        const response: InventoryBulkUpdateResponse = (
            await axios({
                url: API_URL.admin.inventory['bulk-update'].url,
                method: API_URL.admin.inventory['bulk-update'].method,
                data: parsedBody,
            })
        ).data;
        
        return NextResponse.json(response);
    } catch (error) {
        if (error instanceof Error) return NextResponse.json({ message: error.message }, { status: 500 });
        if (error instanceof AxiosError)
            return NextResponse.json({ message: error.response?.data?.message }, { status: 500 });
        if (error instanceof ZodError) return NextResponse.json({ message: error.message }, { status: 400 });
        return NextResponse.json({ message: 'Server error, please try again later.' }, { status: 500 });
    }
}
