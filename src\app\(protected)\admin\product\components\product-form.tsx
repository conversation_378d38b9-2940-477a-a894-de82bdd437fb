'use client';
import ProductImage from '@/app/(protected)/admin/product/components/product-image';
import { Button } from '@/components/ui/button';
import {
    FileUpload,
    FileUploadDropzone,
    FileUploadItem,
    FileUploadItemDelete,
    FileUploadItemMetadata,
    FileUploadItemPreview,
    FileUploadList,
    FileUploadTrigger,
} from '@/components/ui/file-upload';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useCreateProduct, useProduct, useUpdateProduct } from '@/modules/product';
import { CreateProductSchema, createProductSchema } from '@/modules/product/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, UploadIcon, XIcon } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

type PageParams = {
    productId: string;
};

export default function ProductAddEditForm() {
    const params = useParams<PageParams>();
    const isCreate = params.productId === 'create' || params.productId === 'add';
    const [files, setFiles] = useState<File[]>([]);
    const maxSize = 10 * 1024 * 1024; // 10MB default
    const maxFiles = 10;
    const router = useRouter();

    const create = useCreateProduct();
    const update = useUpdateProduct();
    const fn = isCreate ? create : update;
    const productData = useProduct(params.productId);

    const form = useForm({
        resolver: zodResolver(createProductSchema),
        values:
            !isCreate && productData.data
                ? {
                      name: productData.data?.data.product.name,
                      description: productData.data?.data.product.description,
                      sku: productData.data?.data.product.sku,
                      images: productData.data?.data.product.images,
                  }
                : {
                      name: '',
                      description: '',
                      sku: '',
                      images: [],
                  },
    });

    const onFileReject = useCallback((file: File, message: string) => {
        toast.error(message, {
            description: `"${file.name.length > maxFiles ? `${file.name.slice(0, maxFiles)}...` : file.name}" has been rejected`,
        });
    }, []);

    const submit = (data: CreateProductSchema) => {
        const formData = new FormData();
        if (!isCreate) formData.append('productId', params.productId);
        formData.append('name', data.name);
        formData.append('description', data.description);
        formData.append('sku', data.sku);
        files.forEach((file) => {
            formData.append('images', file);
        });

        fn.mutate(formData, {
            onSuccess: () => {
                toast.success(`Product ${isCreate ? 'created' : 'updated'} successfully`);
                if (isCreate) router.push('/admin/product');
            },
        });
    };

    if (productData.isLoading)
        return (
            <div className="flex items-center justify-center">
                <Loader2 className="size-12 animate-spin" />
            </div>
        );
    return (
        <div className="">
            <Form {...form}>
                <form onSubmit={form.handleSubmit(submit)} className="mx-auto max-w-3xl space-y-4">
                    <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Product Name</FormLabel>
                                <FormControl>
                                    <Input placeholder="Product Name" {...field} />
                                </FormControl>

                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Product Description</FormLabel>
                                <FormControl>
                                    <Textarea placeholder="Product Description" {...field} />
                                </FormControl>

                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="sku"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Product SKU</FormLabel>
                                <FormControl>
                                    <Input placeholder="Product SKU" {...field} />
                                </FormControl>

                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    {productData.data?.data?.product?.images && productData.data?.data?.product?.images?.length > 0 ? (
                        <div className="mb-4 grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                            {productData.data?.data.product.images.map((src) => (
                                <ProductImage key={src} src={src} product={productData.data?.data.product} />
                            ))}
                        </div>
                    ) : null}

                    <FileUpload
                        maxFiles={maxFiles}
                        maxSize={maxSize}
                        className="w-full"
                        accept="image/*"
                        value={files}
                        onValueChange={setFiles}
                        onFileReject={onFileReject}
                        multiple
                    >
                        <FileUploadDropzone>
                            <div className="flex flex-col items-center gap-1 text-center">
                                <div className="flex items-center justify-center rounded-full border p-2.5">
                                    <UploadIcon className="text-muted-foreground size-6" />
                                </div>
                                <p className="text-sm font-medium">Drag & drop files here</p>
                                <p className="text-muted-foreground text-xs">
                                    Or click to browse (max 2 files, up to 5MB each)
                                </p>
                            </div>
                            <FileUploadTrigger asChild>
                                <Button type="button" variant="outline" size="sm" className="mt-2 w-fit">
                                    Browse files
                                </Button>
                            </FileUploadTrigger>
                        </FileUploadDropzone>
                        <FileUploadList>
                            {files.map((file, index) => (
                                <FileUploadItem key={index} value={file}>
                                    <FileUploadItemPreview />
                                    <FileUploadItemMetadata />
                                    <FileUploadItemDelete asChild>
                                        <Button variant="ghost" type="button" size="icon" className="size-7">
                                            <XIcon />
                                        </Button>
                                    </FileUploadItemDelete>
                                </FileUploadItem>
                            ))}
                        </FileUploadList>
                    </FileUpload>

                    <Button disabled={create.isPending} type="submit" className="mt-2">
                        {create.isPending && <Loader2 className="size-4 animate-spin" />}
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
}
