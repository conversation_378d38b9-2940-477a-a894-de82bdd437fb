'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { useDeleteProduct } from '@/modules/product';
import { ProductSchema } from '@/modules/product/schema';
import { CellContext, ColumnDef } from '@tanstack/react-table';
import { EditIcon, ImageIcon, Loader2, Trash2Icon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

export const columns: ColumnDef<ProductSchema>[] = [
    {
        accessorKey: 'images',
        header: () => <ImageIcon className="size-4" strokeWidth={1.5} />,
        cell: ProductImages,
    },
    {
        accessorKey: 'name',
        header: 'Name',
    },
    {
        accessorKey: 'sku',
        header: 'sku',
    },
    {
        accessorKey: 'description',
        header: 'Description',
    },
    {
        accessorKey: 'status',
        header: 'Status',
        cell: ProductStatus,
    },
    {
        accessorKey: '_id',
        header: '',
        cell: ProductAction,
    },
];

function ProductImages({ row }: CellContext<ProductSchema, unknown>) {
    const productName = row.original.name;
    const productImages = row.original.images || [];
    const [open, setOpen] = useState(false);

    if (!productImages.length) return <ImageIcon className="size-9.5" strokeWidth={1} />;

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <div className="relative flex aspect-square size-10 cursor-pointer gap-1">
                    {productImages.slice(0, 1).map((src, i) => (
                        <div key={i} className="relative size-full overflow-hidden rounded border">
                            <Image src={src} alt={productName} fill className="object-cover" />
                        </div>
                    ))}

                    {productImages.length > 1 && (
                        <Badge className="border-background absolute -top-1.5 left-full min-w-5 -translate-x-3.5 px-1">
                            {productImages.length - 1}
                        </Badge>
                    )}
                </div>
            </DialogTrigger>
            <DialogContent className="max-w-lg">
                <DialogTitle>{productName}</DialogTitle>
                <Carousel>
                    <CarouselContent>
                        {productImages.map((src, i) => (
                            <CarouselItem key={i}>
                                <div className="relative h-72 w-full">
                                    <Image src={src} alt={productName} fill className="object-contain" sizes="100vw" />
                                </div>
                            </CarouselItem>
                        ))}
                    </CarouselContent>
                    <CarouselPrevious />
                    <CarouselNext />
                </Carousel>
            </DialogContent>
        </Dialog>
    );
}

function ProductStatus({ row }: CellContext<ProductSchema, unknown>) {
    const isActive = row.original.status.toLowerCase() === 'active';
    return <Switch checked={isActive} />;
}

function ProductAction({ row }: CellContext<ProductSchema, unknown>) {
    const deleteProduct = useDeleteProduct();
    return (
        <div className="flex gap-1.5">
            <Button variant="outline" size="icon" asChild>
                <Link href={`/admin/product/${row.original._id}`}>
                    <EditIcon className="size-4" />
                </Link>
            </Button>
            <Button
                onClick={() => deleteProduct.mutate({ productId: row.original._id })}
                disabled={deleteProduct.isPending}
                variant="destructive"
                size="icon"
            >
                {deleteProduct.isPending ? (
                    <Loader2 className="size-4 animate-spin" />
                ) : (
                    <Trash2Icon className="size-4" />
                )}
            </Button>
        </div>
    );
}
